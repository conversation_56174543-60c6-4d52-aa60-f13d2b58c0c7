import { SaveOutlined } from '@ant-design/icons'
import { <PERSON><PERSON>, Card, Checkbox, Col, Form, Input, InputNumber, Row, Select, Spin } from 'antd'
import { useForm } from 'antd/es/form/Form'
import TextArea from 'antd/es/input/TextArea'
import { FC, useEffect, useState } from 'react'
import { enumData } from '~/common/enums/enumData'
import BaseModal from '~/components/BaseModal'
import { ISubscriptionPlan } from '~/dto/subscriptionPlan.dto'
import { useSubscriptionPlan } from '../Hooks/useSubscriptionPlan'

interface IProps {
  data?: ISubscriptionPlan
  open: boolean
  onClose: () => void
}
export const CreateOrEditSubscriptionPlanView: FC<IProps> = ({ data, open, onClose }: IProps) => {
  const [form] = useForm<ISubscriptionPlan>()
  const { loadData, useUpdate, useCreate } = useSubscriptionPlan({ enabled: true })
  const { mutateAsync: updateSubscriptionPlan, isPending: isUpdating } = useUpdate()
  const { mutateAsync: createSubscriptionPlan, isPending: isCreating } = useCreate()
  const [details, setDetails] = useState()

  const handleSave = async (values: any) => {
    if (data) {
      const formattedValues = {
        ...data,
        ...values,
        originalPrice: Number(values.originalPrice) || 0,
        details: details
      }
      await updateSubscriptionPlan(formattedValues)
    } else {
      const formattedValues = {
        ...values,
        originalPrice: Number(values.originalPrice) || 0
      }
      await createSubscriptionPlan(formattedValues).then(() => {
        handleClose()
      })
    }
  }

  const handleClose = () => {
    onClose()
    form.resetFields()
  }

  const modalContent = () => (
    <Spin spinning={data ? isUpdating : isCreating}>
      <Form form={form} layout='vertical' onFinish={handleSave}>
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item label='Tên gói' name='name' rules={[{ required: true, message: 'Vui lòng nhập tên gói!' }]}>
              <Input placeholder='Nhập tên gói' />
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item label='Giá gốc (USD)' name='originalPrice' rules={[{ required: true, message: 'Vui lòng nhập tên gói!' }]}>
              <InputNumber min={0} style={{ width: '100%' }} placeholder='Nhập giá gốc' />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          {/* Note */}
          <Col span={24}>
            <Form.Item label='Miêu tả' name='note'>
              <TextArea rows={4} placeholder='Nhập miêu tả' />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col span={24}>
            <Form.List name='details'>
              {(fields, { add, remove }) => (
                <>
                  <Row style={{ fontWeight: 600, marginBottom: 8 }}>
                    <Col span={2}>Số tháng</Col>
                    <Col span={4}>Đơn giá</Col>
                    <Col span={4}>Giao dịch / tháng</Col>
                    <Col span={4}>Giới hạn dự án</Col>
                    <Col span={4}>Giới hạn cấu hình</Col>
                    <Col span={4}>Body size (byte)</Col>
                    <Col span={2}></Col>
                  </Row>
                  {fields.map(({ key, name, ...restField }) => (
                    <Row gutter={8} key={key} style={{ marginBottom: 8 }}>
                      <Col span={2}>
                        <Form.Item {...restField} name={[name, 'billingCycle']} rules={[{ required: true }]}>
                          <InputNumber min={1} placeholder='1,3,6,9,...' style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={4}>
                        <Form.Item {...restField} name={[name, 'unitPrice']} rules={[{ required: true }]}>
                          <InputNumber min={0} placeholder='$/month' style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={4}>
                        <Form.Item {...restField} name={[name, 'transactionLimit']}>
                          <InputNumber min={0} placeholder='Giới hạn giao dịch' style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={4}>
                        <Form.Item {...restField} name={[name, 'projectLimit']}>
                          <InputNumber min={0} placeholder='Giới hạn dự án' style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={4}>
                        <Form.Item {...restField} name={[name, 'configLimit']}>
                          <InputNumber min={0} placeholder='Giới hạn cấu hình' style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={4}>
                        <Form.Item {...restField} name={[name, 'byteLimit']}>
                          <InputNumber min={0} placeholder='Body size' style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={2}>
                        <Button danger onClick={() => remove(name)}>
                          Xóa
                        </Button>
                      </Col>
                    </Row>
                  ))}
                  <Form.Item>
                    <Button type='dashed' onClick={() => add()} block>
                      + Thêm dòng
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>
          </Col>
        </Row>
        <Row
          style={{
            marginTop: 24,
            borderTop: '1px solid #f0f0f0',
            paddingTop: 16
          }}
          justify={'center'}>
          <Button onClick={handleClose} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            {data ? 'Cập nhật' : 'Tạo mới'}
          </Button>
        </Row>
      </Form>
    </Spin>
  )

  useEffect(() => {
    if (data && open) {
      form.setFieldsValue({
        ...data
      })
    } else {
      form.resetFields()
    }
  }, [open, data, form])

  return <BaseModal open={open} onClose={handleClose} title={data ? 'Chỉnh sửa gói dịch vụ' : 'Tạo gói dịch vụ'} childrenBody={modalContent()} />
}
