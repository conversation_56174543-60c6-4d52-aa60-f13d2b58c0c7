import { EyeOutlined } from '@ant-design/icons'
import BaseButton from '~/components/BaseButton'
import { ISubscriptionPlan } from '~/dto/subscriptionPlan.dto'
import BaseModal from '~/components/BaseModal'
import { Card, Col, Row, Spin, Table, Typography } from 'antd'
import { useModal } from '~/views/global-hooks/useModal'
import { useSubscriptionPlan } from '../Hooks/useSubscriptionPlan'
import { useEffect } from 'react'

interface DetailButtonProps {
  data: ISubscriptionPlan
}

const { Text } = Typography
const DetailButton = ({ data }: DetailButtonProps) => {
  const { details, loadingDetail } = useSubscriptionPlan({ id: data.id })
  const { open, openModal, closeModal } = useModal()

  const columns = [
    {
      title: 'Số tháng',
      dataIndex: 'billingCycle',
      key: 'billingCycle'
    },
    {
      title: 'Đơn giá',
      dataIndex: 'unitPrice',
      key: 'unitPrice'
    },
    {
      title: 'Giao dịch / tháng',
      dataIndex: 'transactionLimit',
      key: 'transactionLimit'
    },
    {
      title: 'Giới hạn dự án',
      dataIndex: 'projectLimit',
      key: 'projectLimit'
    },
    {
      title: 'Giới hạn cấu hình',
      dataIndex: 'configLimit',
      key: 'configLimit'
    },
    {
      title: 'Body size (byte)',
      dataIndex: 'byteLimit',
      key: 'byteLimit'
    }
  ]

  const modalContent = (
    <Card>
      <Row gutter={24}>
        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Mã gói:</Text>
          <p>{data.code}</p>
        </Col>
        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Tên gói:</Text>
          <p>{data.name}</p>
        </Col>
        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Giá gốc:</Text>
          <p>{data.originalPrice}</p>
        </Col>
        <Col span={24}>
          <Text strong>Mô tả:</Text>
          <div
            style={{
              backgroundColor: '#F5F5F5',
              border: '1px solid #D9D9D9',
              borderRadius: 8,
              color: '#595959',
              fontSize: 15,
              padding: 10,
              marginTop: 10
            }}>
            {data.note}
          </div>
        </Col>
      </Row>
      <Row gutter={24} style={{ marginTop: 24 }}>
        <Col span={24}>
          {loadingDetail ? (
            <Spin/>
          ) : (
            <div>
              <Table dataSource={details} columns={columns} pagination={false} rowKey='billingCycle' />
            </div>
          )}
        </Col>
      </Row>
    </Card>
  )

  return (
    <>
      <BaseButton icon={<EyeOutlined />} onClick={openModal} type='primary' />
      <BaseModal open={open} onClose={closeModal} title='Chi tiết gói dịch vụ' childrenBody={modalContent}></BaseModal>
    </>
  )
}

export default DetailButton
